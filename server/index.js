import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fal } from "@fal-ai/client";
import Groq from "groq-sdk";
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from parent directory
dotenv.config({ path: '../.env' });

const app = express();
const PORT = process.env.PORT || 3001;

// Setup ES modules dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create environments directory if it doesn't exist
const environmentsDir = path.join(__dirname, 'environments');
const imagesDir = path.join(__dirname, 'images');
if (!fs.existsSync(environmentsDir)) {
  fs.mkdirSync(environmentsDir, { recursive: true });
}
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Default environment file path
const defaultEnvironmentPath = path.join(environmentsDir, 'default-environment.json');

// Middleware
app.use(cors());
app.use(express.json());

// Initialize AI clients
fal.config({
  credentials: process.env.FAL_KEY
});

const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'AI Service Server is running' });
});

// Fal.ai image generation endpoint
app.post('/api/falai/generate-image', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const defaultOptions = {
      image_size: {
        width: 768,
        height: 1360
      },
      num_inference_steps: 16,
      num_images: 1,
      enable_safety_checker: true,
      output_format: "jpeg",
      sync_mode: true,
      ...options
    };

    console.log('Generating image with prompt:', prompt);
    
    const result = await fal.subscribe("fal-ai/hidream-i1-fast", {
      input: {
        prompt: prompt,
        negative_prompt: options.negative_prompt || "",
        image_size: defaultOptions.image_size,
        num_inference_steps: defaultOptions.num_inference_steps,
        num_images: defaultOptions.num_images,
        enable_safety_checker: defaultOptions.enable_safety_checker,
        output_format: defaultOptions.output_format
      },
      logs: true,
    });

    console.log('Image generation completed');
    res.json(result.data);
  } catch (error) {
    console.error('Error generating image:', error);
    res.status(500).json({ error: `Failed to generate image: ${error.message}` });
  }
});

// Groq LLM endpoint
app.post('/api/groq/generate-completion', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const defaultOptions = {
      model: "llama-3.1-8b-instant",
      temperature: 0.7,
      max_tokens: 1000,
      ...options
    };

    console.log('Generating completion with prompt length:', prompt.length);
    
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      model: defaultOptions.model,
      temperature: defaultOptions.temperature,
      max_tokens: defaultOptions.max_tokens,
    });

    const response = chatCompletion.choices[0]?.message?.content || "";
    console.log('Completion generated');
    res.json({ response });
  } catch (error) {
    console.error('Error generating completion:', error);
    res.status(500).json({ error: `Failed to generate completion: ${error.message}` });
  }
});

// Function to save default environment to file
function saveDefaultEnvironment(environmentData) {
  try {
    fs.writeFileSync(defaultEnvironmentPath, JSON.stringify(environmentData, null, 2));
    console.log('Default environment saved to file');
  } catch (error) {
    console.error('Error saving default environment:', error);
  }
}

// Function to load default environment from file
function loadDefaultEnvironment() {
  try {
    if (fs.existsSync(defaultEnvironmentPath)) {
      const data = fs.readFileSync(defaultEnvironmentPath, 'utf8');
      console.log('Default environment loaded from file');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading default environment:', error);
  }
  return null;
}

// Endpoint to get default environment (checks file system first)
app.get('/api/default-environment', async (req, res) => {
  try {
    // Check if we have a saved default environment
    const savedEnvironment = loadDefaultEnvironment();
    
    if (savedEnvironment) {
      console.log('Returning saved default environment');
      return res.json(savedEnvironment);
    }
    
    // If no saved environment, generate one
    console.log('No saved default environment found, generating one...');
    
    const defaultDescription = "Deep space battlefield with stars and nebulae";
    
    // Step 1: Generate environment details using Groq
    const envPrompt = `You are an expert graphic designer for a vertical space shooter game. Based on the environment description, generate:

1. A detailed description a background image for our vertical scrolling shooter game, which describes the image, not the game, or the properties of the image. It does not include instructions of any kind. It should not say "Generate a..." It is to be purely descriptive of what is shown in the image.
2. JSON configuration for gameplay modifiers that describe how this environment affects enemies and gameplay

Environment description: "${defaultDescription}"

The gameplay modifiers should describe how the environment affects enemies in the game. For example:
- Ice environment might slow down enemies
- Volcanic environment might increase enemy speed but reduce their health
- Space environment might have normal stats
- Jungle environment might provide cover for enemies (increased health)
- Cyberpunk environment might have fast but fragile enemies
- Alien environment might have unique enemy behaviors

**Enemy Types and Strengths/Vulnerabilities**:
- **Water**: Strong against Fire, weak against Earth.
- **Fire**: Strong against Air, weak against Water.
- **Air**: Strong against Earth, weak against Fire.
- **Earth**: Strong against Water, weak against Air.
- **Crystal**: Resistant to most types, but vulnerable to sound and focused attacks.
- **Shadow**: Fast and evasive, but vulnerable to laser blasts and light environments.

Please respond in the following JSON format:
{
  "imagePrompt": "Detailed description for Fal.ai image generation",
  "gameplayModifiers": {
    "enemySpeedMultiplier": 0.5-2.0,
    "enemyHealthMultiplier": 0.5-2.0,
    "enemySpawnRateMultiplier": 0.5-2.0,
    "enemyProjectileSpeedMultiplier": 0.5-2.0,
    "environmentEffects": [
      "Effect description 1 - these are the gameplay modifiers such as slows down enemies if an ice environment",
      "Effect description 2"
    ],
    "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"],
    "environmentHazards": [
      {
        "type": "asteroid_field|lava_pools|ice_patches|electrical_storms",
        "damagePerSecond": 0-10,
        "slowEffect": 0.0-1.0
      }
    ],
    "enemyTypeModifiers": {
      "water": 0.5-2.0,
      "fire": 0.5-2.0,
      "air": 0.5-2.0,
      "earth": 0.5-2.0,
      "crystal": 0.5-2.0,
      "shadow": 0.5-2.0
    }
  }
}

Ensure the JSON response is valid JSON and the values are realistic for game balance. The image should be suitable for a vertical scrolling space shooter game in portrait orientation.`;

    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: envPrompt,
        },
      ],
      model: "llama-3.1-8b-instant",
      temperature: 0.8,
      max_tokens: 1500,
    });

    const llmResponse = chatCompletion.choices[0]?.message?.content || "";
    
    // Parse the response to extract structured data
    let parsedResponse;
    try {
      // Try to parse as JSON directly
      parsedResponse = JSON.parse(llmResponse);
    } catch (error) {
      console.log('Direct JSON parsing failed, attempting to extract JSON from response:', llmResponse);
      
      // Try to extract JSON from the response
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        console.log('Extracted JSON string:', jsonStr);
        
        // Fix common JSON issues
        const fixedJsonStr = jsonStr
          .replace(/,\s*([}\]])/g, '$1') // Remove trailing commas
          .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":') // Ensure all property names are quoted
          .replace(/'/g, '"'); // Replace single quotes with double quotes
        
        console.log('Fixed JSON string:', fixedJsonStr);
        
        try {
          parsedResponse = JSON.parse(fixedJsonStr);
        } catch (fixError) {
          console.error('Failed to parse fixed JSON:', fixError);
          // Return a fallback response
          parsedResponse = {
            imagePrompt: "Epic space battlefield with distant stars, nebulae, and galaxies. Deep cosmic background with subtle starfield, perfect for a vertical space shooter game.",
            gameplayModifiers: {
              enemySpeedMultiplier: 1.0,
              enemyHealthMultiplier: 1.0,
              enemySpawnRateMultiplier: 1.0,
              enemyProjectileSpeedMultiplier: 1.0,
              environmentEffects: ["Normal space environment"],
              compatibleEnemyTypes: ["water", "fire", "air", "earth", "crystal", "shadow"],
              environmentHazards: [],
              enemyTypeModifiers: {
                water: 1.0,
                fire: 1.0,
                air: 1.0,
                earth: 1.0,
                crystal: 1.0,
                shadow: 1.0
              }
            }
          };
        }
      } else {
        console.error('No JSON found in LLM response');
        // Return a fallback response
        parsedResponse = {
          imagePrompt: "Epic space battlefield with distant stars, nebulae, and galaxies. Deep cosmic background with subtle starfield, perfect for a vertical space shooter game.",
          gameplayModifiers: {
            enemySpeedMultiplier: 1.0,
            enemyHealthMultiplier: 1.0,
            enemySpawnRateMultiplier: 1.0,
            enemyProjectileSpeedMultiplier: 1.0,
            environmentEffects: ["Normal space environment"],
            compatibleEnemyTypes: ["water", "fire", "air", "earth", "crystal", "shadow"],
            environmentHazards: [],
            enemyTypeModifiers: {
              water: 1.0,
              fire: 1.0,
              air: 1.0,
              earth: 1.0,
              crystal: 1.0,
              shadow: 1.0
            }
          }
        };
      }
    }
    
    // Step 2: Generate image using Fal.ai
    const imageResult = await fal.subscribe("fal-ai/hidream-i1-fast", {
      input: {
        prompt: parsedResponse.imagePrompt,
        negative_prompt: "blurry, low quality, text, watermark, signature, distorted, ugly",
        image_size: {
          width: 768,
          height: 1360
        },
        num_inference_steps: 16,
        num_images: 1,
        enable_safety_checker: true,
        output_format: "jpeg"
      },
      logs: true,
    });

    const environmentData = {
      imagePrompt: parsedResponse.imagePrompt,
      gameplayModifiers: parsedResponse.gameplayModifiers,
      imageData: imageResult.data
    };
    
    // Save the default environment for future use
    saveDefaultEnvironment(environmentData);
    
    console.log('Default environment generated and saved');
    res.json(environmentData);
  } catch (error) {
    console.error('Error generating default environment:', error);
    res.status(500).json({ error: `Failed to generate default environment: ${error.message}` });
  }
});

// Environment generation endpoint (combines both services)
app.post('/api/generate-environment', async (req, res) => {
  try {
    const { environmentDescription = "space" } = req.body;
    
    console.log('Generating environment for:', environmentDescription);
    
    // Check if this is a request for the default environment
    if (environmentDescription.toLowerCase().includes("deep space battlefield") ||
        environmentDescription.toLowerCase().includes("default") ||
        environmentDescription.toLowerCase().includes("space battlefield with stars and nebulae")) {
      
      // For default environment, check if we have it saved
      const savedEnvironment = loadDefaultEnvironment();
      if (savedEnvironment) {
        console.log('Returning saved default environment for generate-environment request');
        return res.json(savedEnvironment);
      }
    }
    
    // Step 1: Generate environment details using Groq
    const envPrompt = `You are an expert graphic designer for a vertical space shooter game. Based on the environment description, generate:

1. A detailed Fal.ai image generation prompt for creating a background image
2. JSON configuration for gameplay modifiers that describe how this environment affects enemies and gameplay. DO NOT MAKE ANY COMMENTS ON ANYTHING. RESPOND WITH ONLY THE DESIRED RESULT AS INSTRUCTED, NO EXPLANATIONS OR CONVERSATIONS.

Environment description: "${environmentDescription}"

The gameplay modifiers should describe how the environment affects enemies in the game. For example:
- Ice environment might slow down enemies
- Volcanic environment might increase enemy speed but reduce their health
- Space environment might have normal stats
- Jungle environment might provide cover for enemies (increased health)
- Cyberpunk environment might have fast but fragile enemies
- Alien environment might have unique enemy behaviors

**Enemy Types and Strengths/Vulnerabilities**:
- **Water**: Strong against Fire, weak against Earth.
- **Fire**: Strong against Air, weak against Water.
- **Air**: Strong against Earth, weak against Fire.
- **Earth**: Strong against Water, weak against Air.
- **Crystal**: Resistant to most types, but vulnerable to sound and focused attacks.
- **Shadow**: Fast and evasive, but vulnerable to laser blasts and light environments.

Please respond in the following JSON format:
{
  "imagePrompt": "Detailed description for Fal.ai image generation",
  "gameplayModifiers": {
    "enemySpeedMultiplier": 0.5-2.0,
    "enemyHealthMultiplier": 0.5-2.0,
    "enemySpawnRateMultiplier": 0.5-2.0,
    "enemyProjectileSpeedMultiplier": 0.5-2.0,
    "environmentEffects": [
      "Effect description 1 - these are the gameplay modifiers such as slows down enemies if an ice environment",
      "Effect description 2"
    ],
    "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"],
    "environmentHazards": [
      {
        "type": "asteroid_field|lava_pools|ice_patches|electrical_storms",
        "damagePerSecond": 0-10,
        "slowEffect": 0.0-1.0
      }
    ],
    "enemyTypeModifiers": {
      "water": 0.5-2.0,
      "fire": 0.5-2.0,
      "air": 0.5-2.0,
      "earth": 0.5-2.0,
      "crystal": 0.5-2.0,
      "shadow": 0.5-2.0
    }
  }
}

Ensure the JSON response is valid JSON and the values are realistic for game balance. The image should be suitable for a vertical scrolling space shooter game. That means your prompt should describe an image suitable for this format. It should not describe the format itself. Don't be an idiot.`;

    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: envPrompt,
        },
      ],
      model: "llama-3.1-8b-instant",
      temperature: 0.8,
      max_tokens: 1500,
    });

    const llmResponse = chatCompletion.choices[0]?.message?.content || "";
    
    // Parse the response to extract structured data
    let parsedResponse;
    try {
      // Try to parse as JSON directly
      parsedResponse = JSON.parse(llmResponse);
    } catch (error) {
      console.log('Direct JSON parsing failed, attempting to extract JSON from response:', llmResponse);
      
      // Try to extract JSON from the response
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        console.log('Extracted JSON string:', jsonStr);
        
        // Fix common JSON issues
        const fixedJsonStr = jsonStr
          .replace(/,\s*([}\]])/g, '$1') // Remove trailing commas
          .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":') // Ensure all property names are quoted
          .replace(/'/g, '"') // Replace single quotes with double quotes
          .replace(/"9":16"/g, '"9:16"') // Fix specific issue with aspect ratio
          .replace(/\\n/g, '\\\\n') // Escape newlines properly
          .replace(/\\t/g, '\\\\t'); // Escape tabs properly
        
        console.log('Fixed JSON string:', fixedJsonStr);
        
        try {
          parsedResponse = JSON.parse(fixedJsonStr);
        } catch (fixError) {
          console.error('Failed to parse fixed JSON:', fixError);
          // Return a fallback response
          parsedResponse = {
            imagePrompt: "Space background with stars and nebulae",
            gameplayModifiers: {
              enemySpeedMultiplier: 1.0,
              enemyHealthMultiplier: 1.0,
              enemySpawnRateMultiplier: 1.0,
              enemyProjectileSpeedMultiplier: 1.0,
              environmentEffects: ["Normal space environment"],
              compatibleEnemyTypes: ["water", "fire", "air", "earth", "crystal", "shadow"],
              environmentHazards: [],
              enemyTypeModifiers: {
                water: 1.0,
                fire: 1.0,
                air: 1.0,
                earth: 1.0,
                crystal: 1.0,
                shadow: 1.0
              }
            }
          };
        }
      } else {
        console.error('No JSON found in LLM response');
        // Return a fallback response
        parsedResponse = {
          imagePrompt: "Space background with stars and nebulae",
          gameplayModifiers: {
            enemySpeedMultiplier: 1.0,
            enemyHealthMultiplier: 1.0,
            enemySpawnRateMultiplier: 1.0,
            enemyProjectileSpeedMultiplier: 1.0,
            environmentEffects: ["Normal space environment"],
            compatibleEnemyTypes: ["water", "fire", "air", "earth", "crystal", "shadow"],
            environmentHazards: [],
            enemyTypeModifiers: {
              water: 1.0,
              fire: 1.0,
              air: 1.0,
              earth: 1.0,
              crystal: 1.0,
              shadow: 1.0
            }
          }
        };
      }
    }
    
    // Step 2: Generate image using Fal.ai
    const imageResult = await fal.subscribe("fal-ai/hidream-i1-fast", {
      input: {
        prompt: parsedResponse.imagePrompt,
        negative_prompt: "blurry, low quality, text, watermark, signature, distorted, ugly",
        image_size: {
          width: 768,
          height: 1360
        },
        num_inference_steps: 16,
        num_images: 1,
        enable_safety_checker: true,
        output_format: "jpeg"
      },
      logs: true,
    });

    // Step 3: Save the generated image to the server
    let savedImagePath = null;
    if (imageResult.data && imageResult.data.images && imageResult.data.images[0] && imageResult.data.images[0].url) {
      try {
          const imageUrl = imageResult.data.images[0].url;
          const imageResponse = await fetch(imageUrl);
          
          if (!imageResponse.ok) {
              throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
          }
          
          const imageArrayBuffer = await imageResponse.arrayBuffer();
          const imageBuffer = Buffer.from(imageArrayBuffer);
          
          // Generate a unique filename based on timestamp and environment description
          const timestamp = Date.now();
          const safeDescription = environmentDescription.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '-').toLowerCase();
          const filename = `${timestamp}-${safeDescription}.jpg`;
          savedImagePath = path.join(imagesDir, filename);
          
          // Save the image to disk
          fs.writeFileSync(savedImagePath, imageBuffer);
          console.log('Image saved to:', savedImagePath);
          
          // Update the image data to use the local path
          imageResult.data.images[0].url = `/api/images/${filename}`;
          imageResult.data.images[0].localPath = savedImagePath;
      } catch (imageError) {
          console.error('Failed to save image:', imageError);
          // Continue with the original URL if saving fails
      }
    }

    console.log('Environment generated successfully');
    res.json({
      imagePrompt: parsedResponse.imagePrompt,
      gameplayModifiers: parsedResponse.gameplayModifiers,
      imageData: imageResult.data,
      savedImagePath: savedImagePath
    });
  } catch (error) {
    console.error('Error generating environment:', error);
    res.status(500).json({ error: `Failed to generate environment: ${error.message}` });
  }
});

// Serve saved images
app.get('/api/images/:filename', (req, res) => {
  const filename = req.params.filename;
  const imagePath = path.join(imagesDir, filename);
  
  if (fs.existsSync(imagePath)) {
    res.sendFile(imagePath);
  } else {
    res.status(404).json({ error: 'Image not found' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`AI Service Server running on port ${PORT}`);
});