import { FalaiClient } from './FalaiClient.js';
import { LLMClient } from './LLMClient.js';

/**
 * EnvironmentCreator class for generating environment details and coordinating AI services
 * Takes user input and creates both the Fal.ai prompt and gameplay modifiers
 */
export class EnvironmentCreator {
    constructor() {
        this.falaiClient = new FalaiClient();
        this.llmClient = new LLMClient();
        this.isInitialized = false;
        this.cache = new Map();
        this.cacheExpiry = 30 * 60 * 1000; // 30 minutes cache expiry
    }

    /**
     * Initialize the AI clients
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            await this.falaiClient.initialize();
            await this.llmClient.initialize();
            this.isInitialized = true;
            console.log('EnvironmentCreator initialized successfully');
        } catch (error) {
            console.error('Failed to initialize EnvironmentCreator:', error);
            throw error;
        }
    }

    /**
     * Create a new environment based on user input
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Complete environment data with image and modifiers
     */
    async createEnvironment(userInput) {
        console.log('🔍 [ENV CREATOR DEBUG] createEnvironment called with input:', userInput);
        console.log('🔍 [ENV CREATOR DEBUG] isInitialized:', this.isInitialized);
        
        if (!this.isInitialized) {
            console.log('🔍 [ENV CREATOR DEBUG] Initializing EnvironmentCreator...');
            await this.initialize();
        }

        // Check cache first
        const cacheKey = `env_create:${userInput}`;
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            console.log('🔍 [ENV CREATOR DEBUG] Returning cached environment creation result');
            return cachedResult;
        }

        try {
            console.log('🔍 [ENV CREATOR DEBUG] No cache found, making API request to server...');
            console.log('🔍 [ENV CREATOR DEBUG] Request URL: http://localhost:3001/api/generate-environment');
            console.log('🔍 [ENV CREATOR DEBUG] Request body:', { environmentDescription: userInput });
            
            // Use the combined environment generation endpoint
            const response = await fetch('http://localhost:3001/api/generate-environment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    environmentDescription: userInput
                }),
            });

            console.log('🔍 [ENV CREATOR DEBUG] Server response status:', response.status);
            
            if (!response.ok) {
                const errorData = await response.json();
                console.error('❌ [ENV CREATOR DEBUG] Server error response:', errorData);
                throw new Error(errorData.error || 'Failed to generate environment');
            }

            const result = await response.json();
            console.log('🔍 [ENV CREATOR DEBUG] Server response data:', result);
            
            // Load the generated image
            console.log('🔍 [ENV CREATOR DEBUG] Loading image from URL:', result.imageData?.images?.[0]?.url);
            const backgroundImage = await this.loadImageFromUrl(result.imageData.images[0].url);
            console.log('🔍 [ENV CREATOR DEBUG] Image loaded successfully:', !!backgroundImage);
            
            // Create complete environment data
            const environmentData = {
                type: this.determineEnvironmentType(userInput),
                name: this.generateEnvironmentName(userInput),
                description: userInput,
                imagePrompt: result.imagePrompt,
                gameplayModifiers: result.gameplayModifiers,
                imageData: result.imageData,
                backgroundImage: backgroundImage
            };
            
            // Cache the result
            this.setToCache(cacheKey, environmentData);
            
            console.log('✅ [ENV CREATOR DEBUG] Environment created successfully:', environmentData);
            return environmentData;
            
        } catch (error) {
            console.error('❌ [ENV CREATOR DEBUG] Error creating environment:', error);
            throw new Error(`Failed to create environment: ${error.message}`);
        }
    }

    /**
     * Create environment with default space environment
     * @returns {Promise<object>} - Default environment data
     */
    async createDefaultEnvironment() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Check cache first
        const cacheKey = 'env_default';
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            console.log('Returning cached default environment');
            return cachedResult;
        }

        try {
            console.log('Creating default space environment');
            
            // Use the dedicated default environment endpoint
            const response = await fetch('http://localhost:3001/api/default-environment', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to generate default environment');
            }

            const result = await response.json();
            
            // Load the generated image
            const backgroundImage = await this.loadImageFromUrl(result.imageData.images[0].url);
            
            // Create complete environment data
            const environmentData = {
                type: "space",
                name: "Deep Space",
                description: "Classic space environment perfect for intense dogfights",
                imagePrompt: result.imagePrompt,
                gameplayModifiers: result.gameplayModifiers,
                imageData: result.imageData,
                backgroundImage: backgroundImage
            };
            
            // Cache the result
            this.setToCache(cacheKey, environmentData);
            
            console.log('Default environment created successfully');
            return environmentData;
            
        } catch (error) {
            console.error('Error creating default environment:', error);
            throw new Error(`Failed to create default environment: ${error.message}`);
        }
    }

    /**
     * Generate environment details only (without image generation)
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Environment details with prompts and modifiers
     */
    async generateEnvironmentDetails(userInput) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Check cache first
        const cacheKey = `env_details:${userInput}`;
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            console.log('Returning cached environment details');
            return cachedResult;
        }

        try {
            console.log('Generating environment details for:', userInput);
            
            // Use LLM to generate both image prompt and gameplay modifiers
            const llmResponse = await this.llmClient.generateRealityWarpPrompt({}, {}, 'basic', userInput);
            
            const environmentDetails = {
                type: this.determineEnvironmentType(userInput),
                name: this.generateEnvironmentName(userInput),
                description: userInput,
                imagePrompt: llmResponse.imagePrompt,
                gameplayModifiers: llmResponse.gameplayModifiers
            };
            
            // Cache the result
            this.setToCache(cacheKey, environmentDetails);
            
            return environmentDetails;
            
        } catch (error) {
            console.error('Error generating environment details:', error);
            throw new Error(`Failed to generate environment details: ${error.message}`);
        }
    }

    /**
     * Generate image from pre-existing environment details
     * @param {object} environmentDetails - Environment details with image prompt
     * @returns {Promise<HTMLImageElement>} - Generated background image
     */
    async generateImageFromDetails(environmentDetails) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            console.log('Generating image from environment details');
            
            // Use Fal.ai to generate the background image
            const imageData = await this.falaiClient.generateImage(environmentDetails.imagePrompt, {
                negative_prompt: "blurry, low quality, text, watermark, signature, distorted, ugly"
            });
            
            // Load the generated image
            const backgroundImage = await this.loadImageFromUrl(imageData.images[0].url);
            
            return backgroundImage;
            
        } catch (error) {
            console.error('Error generating image from details:', error);
            throw new Error(`Failed to generate image from details: ${error.message}`);
        }
    }

    /**
     * Determine environment type from user input
     * @param {string} userInput - User's description
     * @returns {string} - Determined environment type
     */
    determineEnvironmentType(userInput) {
        const input = userInput.toLowerCase();
        
        const typeKeywords = {
            space: ['space', 'star', 'galaxy', 'nebula', 'cosmic', 'void', 'universe'],
            alien: ['alien', 'extraterrestrial', 'foreign', 'strange', 'unknown', 'mysterious'],
            cyberpunk: ['cyberpunk', 'neon', 'city', 'futuristic', 'tech', 'digital', 'hologram'],
            volcanic: ['volcanic', 'lava', 'fire', 'magma', 'volcano', 'heat', 'molten'],
            ice: ['ice', 'frozen', 'snow', 'cold', 'glacier', 'arctic', 'tundra'],
            jungle: ['jungle', 'forest', 'vegetation', 'tropical', 'lush', 'green', 'organic']
        };

        for (const [type, keywords] of Object.entries(typeKeywords)) {
            if (keywords.some(keyword => input.includes(keyword))) {
                return type;
            }
        }

        return 'space'; // Default to space
    }

    /**
     * Generate a nice environment name from user input
     * @param {string} userInput - User's description
     * @returns {string} - Generated environment name
     */
    generateEnvironmentName(userInput) {
        const type = this.determineEnvironmentType(userInput);
        const typeNames = {
            space: 'Deep Space',
            alien: 'Alien World',
            cyberpunk: 'Neo-Tokyo',
            volcanic: 'Volcanic Wasteland',
            ice: 'Frozen Planet',
            jungle: 'Alien Jungle'
        };

        return typeNames[type] || 'Custom Environment';
    }

    /**
     * Load image from URL
     * @param {string} url - Image URL
     * @returns {Promise<HTMLImageElement>} - Loaded image
     */
    loadImageFromUrl(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            img.onload = () => {
                console.log('Image loaded successfully:', url);
                resolve(img);
            };
            
            img.onerror = () => {
                console.error('Failed to load image:', url);
                reject(new Error(`Failed to load image: ${url}`));
            };
            
            img.src = url;
        });
    }

    /**
     * Get data from cache if it exists and hasn't expired
     * @param {string} key - Cache key
     * @returns {object|null} - Cached data or null
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
            return cached.data;
        }
        if (cached) {
            this.cache.delete(key);
        }
        return null;
    }

    /**
     * Set data in cache with timestamp
     * @param {string} key - Cache key
     * @param {object} data - Data to cache
     */
    setToCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    /**
     * Clear expired cache entries
     */
    clearExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > this.cacheExpiry) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * Get cache statistics
     * @returns {object} - Cache stats
     */
    getCacheStats() {
        return {
            totalEntries: this.cache.size,
            expiryTime: this.cacheExpiry,
            keys: Array.from(this.cache.keys())
        };
    }

    /**
     * Clear all cache entries
     */
    clearCache() {
        this.cache.clear();
        console.log('EnvironmentCreator cache cleared');
    }

    /**
     * Get creator status
     * @returns {object} - Creator status
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            cacheStats: this.getCacheStats(),
            falaiClientInitialized: this.falaiClient.isInitialized,
            llmClientInitialized: this.llmClient.isInitialized
        };
    }
}