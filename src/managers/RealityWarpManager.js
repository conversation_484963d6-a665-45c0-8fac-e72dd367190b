import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * RealityWarpManager handles reality warp functionality, including configurations,
 * cost calculations, state management, and coordination with other game systems
 */
export class RealityWarpManager {
    constructor(tokenEconomyManager = null, llmClient = null, levelManager = null) {
        // Dependencies
        this.tokenEconomyManager = tokenEconomyManager;
        this.llmClient = null; // LLM client is now handled by backend API
        this.levelManager = levelManager;
        
        // Warp state management
        this.warpState = {
            status: 'ready', // ready, active, cooldown, disabled
            cooldownEndTime: 0,
            activeEndTime: 0,
            lastWarpTime: 0,
            warpCount: 0,
            maxWarpsPerSession: 3
        };
        
        // Debug and logging configuration
        this.debugMode = GAME_CONFIG.DEBUG_MODE;
        this.logEnabled = GAME_CONFIG.ENABLE_CONSOLE_LOGS;
        this.performanceMetricsEnabled = true;
        this.costCalculationLogging = true;
        this.transactionLogging = true;
        
        // Performance tracking for cost calculations
        this.costCalculationMetrics = {
            totalCalculations: 0,
            averageCalculationTime: 0,
            calculationTimes: [],
            lastCalculationTime: 0
        };
        
        // Transaction tracking
        this.transactionMetrics = {
            totalTransactions: 0,
            successfulTransactions: 0,
            failedTransactions: 0,
            lastTransactionTime: 0
        };
        
        // Warp configuration - single unified Reality Warp
        this.warpConfigurations = {
            // Single Reality Warp type
            basic: {
                id: 'basic',
                name: 'Reality Warp',
                description: 'Transform the next level with your imagination and reshape the battlefield',
                baseCost: GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP,
                complexity: 2,
                duration: 8000, // 8 seconds
                cooldown: 45000, // 45 seconds
                effects: ['enemy_freeze', 'weapon_enhance', 'shield_regenerate']
            }
        };
        
        // Current warp session data
        this.currentWarp = {
            type: null,
            startTime: 0,
            effects: [],
            cost: 0,
            generatedPrompt: null
        };
        
        // Warp history for analytics
        this.warpHistory = [];
        this.maxHistorySize = 50;
        
        // Warp performance metrics
        this.performanceMetrics = {
            totalWarps: 0,
            successfulWarps: 0,
            failedWarps: 0,
            totalCost: 0,
            averageEffectiveness: 0,
            effectivenessScores: []
        };
        
        // Event callbacks
        this.onWarpStartCallback = null;
        this.onWarpEndCallback = null;
        this.onWarpCooldownStartCallback = null;
        this.onWarpCooldownEndCallback = null;
        this.onWarpCostCalculatedCallback = null;
        this.onPromptGeneratedCallback = null;
        
        // Debug and logging
        this.debugMode = GAME_CONFIG.DEBUG_MODE;
        this.logEnabled = GAME_CONFIG.ENABLE_CONSOLE_LOGS;
        
        console.log('RealityWarpManager initialized');
    }
    
    /**
     * Initialize the RealityWarpManager
     * @param {object} config - Configuration options
     * @returns {boolean} Initialization success status
     */
    init(config = {}) {
        try {
            console.log('Initializing RealityWarpManager with config:', config);
            
            // Apply configuration if provided
            if (config.warpConfigurations) {
                this.warpConfigurations = { ...this.warpConfigurations, ...config.warpConfigurations };
            }
            
            if (config.maxWarpsPerSession) {
                this.warpState.maxWarpsPerSession = config.maxWarpsPerSession;
            }
            
            // Validate dependencies
            if (!this.tokenEconomyManager) {
                console.warn('TokenEconomyManager not provided - cost calculations will be limited');
            }
            
            // LLMClient is now handled by backend API
            
            if (!this.levelManager) {
                console.warn('LevelManager not provided - level coordination will be limited');
            }
            
            // Initialize warp state
            this.warpState.status = 'ready';
            this.warpState.cooldownEndTime = 0;
            this.warpState.activeEndTime = 0;
            
            console.log('RealityWarpManager initialized successfully');
            return true;
            
        } catch (error) {
            console.error('Failed to initialize RealityWarpManager:', error);
            return false;
        }
    }
    
    /**
     * Check if user can perform a warp
     * @param {string} warpType - Type of warp to check
     * @returns {object} Warp eligibility result
     */
    canWarp(warpType = 'basic') {
        try {
            const warpConfig = this.warpConfigurations[warpType];
            if (!warpConfig) {
                return { 
                    canWarp: false, 
                    reason: 'invalid_warp_type',
                    message: `Invalid warp type: ${warpType}`
                };
            }
            
            // Check if warp is disabled
            if (this.warpState.status === 'disabled') {
                return { 
                    canWarp: false, 
                    reason: 'system_disabled',
                    message: 'Reality warp system is currently disabled'
                };
            }
            
            // Check if on cooldown
            if (this.warpState.status === 'cooldown') {
                const remainingCooldown = this.warpState.cooldownEndTime - Date.now();
                if (remainingCooldown > 0) {
                    return { 
                        canWarp: false, 
                        reason: 'on_cooldown',
                        message: 'On cooldown',
                        remainingCooldown: remainingCooldown
                    };
                }
            }
            
            // Check if already active
            if (this.warpState.status === 'active') {
                return { 
                    canWarp: false, 
                    reason: 'already_active',
                    message: 'Reality warp is already active'
                };
            }
            
            // Check warp session limit
            if (this.warpState.warpCount >= this.warpState.maxWarpsPerSession) {
                return { 
                    canWarp: false, 
                    reason: 'session_limit_reached',
                    message: `Maximum warps per session reached (${this.warpState.maxWarpsPerSession})`
                };
            }
            
            // Check if user can afford the warp
            const cost = this.calculateCost(warpType);
            if (this.tokenEconomyManager && !this.tokenEconomyManager.canAfford(cost)) {
                return { 
                    canWarp: false, 
                    reason: 'insufficient_tokens',
                    message: 'Insufficient tokens',
                    required: cost,
                    available: this.tokenEconomyManager.getBalance()
                };
            }
            
            // If all checks passed
            return { 
                canWarp: true, 
                reason: 'eligible',
                message: 'Can perform warp',
                cost: cost,
                warpConfig: warpConfig
            };
            
        } catch (error) {
            console.error('Error checking warp eligibility:', error);
            return { 
                canWarp: false, 
                reason: 'error',
                message: 'Error checking warp eligibility'
            };
        }
    }
    
    /**
     * Calculate base cost for warp type and level
     * @param {string} warpType - Type of warp (basic, advanced, ultimate)
     * @param {number} level - Current level
     * @returns {number} Base cost amount
     */
    calculateBaseCost(warpType, level = 1) {
        try {
            if (!this.tokenEconomyManager) {
                console.warn('TokenEconomyManager not available for cost calculation');
                return this.getFallbackBaseCost(warpType);
            }
            
            return this.tokenEconomyManager.calculateBaseCost(warpType, level);
            
        } catch (error) {
            console.error('Error calculating base cost:', error);
            return this.getFallbackBaseCost(warpType);
        }
    }
    
    /**
     * Get fallback base cost when TokenEconomyManager is unavailable
     * @private
     * @param {string} warpType - Type of warp
     * @returns {number} Fallback cost
     */
    getFallbackBaseCost(warpType) {
        const warpConfig = this.warpConfigurations[warpType];
        if (!warpConfig) {
            return GAME_CONFIG.WARP_BASE_COST;
        }
        return warpConfig.baseCost;
    }
    
    /**
     * Apply session-based discount for consecutive warps
     * @param {number} baseCost - Base cost before discount
     * @param {number} sessionWarps - Number of warps in current session
     * @returns {number} Cost after session discount
     */
    applySessionDiscount(baseCost, sessionWarps = 0) {
        try {
            if (!this.tokenEconomyManager) {
                // Fallback session discount calculation
                let discountMultiplier = 1.0;
                if (sessionWarps === 0) {
                    discountMultiplier = 0.8; // 20% discount for first warp
                } else if (sessionWarps <= 2) {
                    discountMultiplier = 0.9; // 10% discount for second and third warp
                }
                return Math.round(baseCost * discountMultiplier);
            }
            
            return this.tokenEconomyManager.applySessionDiscount(baseCost, sessionWarps);
            
        } catch (error) {
            console.error('Error applying session discount:', error);
            return baseCost;
        }
    }
    
    /**
     * Apply performance-based cost adjustment
     * @param {number} baseCost - Base cost before adjustment
     * @param {object} playerStats - Player performance statistics
     * @returns {number} Cost after performance adjustment
     */
    applyPerformanceMultiplier(baseCost, playerStats = {}) {
        try {
            if (!this.tokenEconomyManager) {
                // Fallback performance calculation
                let performanceMultiplier = 1.0;
                
                // Simple performance adjustment based on provided stats
                if (playerStats.accuracy && playerStats.accuracy > 0.8) {
                    performanceMultiplier = 0.9; // 10% discount for high accuracy
                }
                
                return Math.round(baseCost * performanceMultiplier);
            }
            
            return this.tokenEconomyManager.applyPerformanceMultiplier(baseCost, playerStats);
            
        } catch (error) {
            console.error('Error applying performance multiplier:', error);
            return baseCost;
        }
    }
    
    /**
     * Calculate final cost for a warp with all adjustments
     * @param {string} warpType - Type of warp
     * @param {number} level - Current level
     * @param {number} sessionWarps - Number of warps in current session
     * @param {object} playerStats - Player performance statistics
     * @returns {number} Final calculated cost
     */
    getFinalCost(warpType, level, sessionWarps, playerStats = {}) {
        try {
            if (!this.tokenEconomyManager) {
                console.warn('TokenEconomyManager not available for final cost calculation');
                return this.calculateBaseCost(warpType, level);
            }
            
            return this.tokenEconomyManager.getFinalCost(warpType, level, sessionWarps, playerStats);
            
        } catch (error) {
            console.error('Error calculating final cost:', error);
            return this.calculateBaseCost(warpType, level);
        }
    }
    
    /**
     * Calculate token cost for a warp (legacy method - delegates to enhanced calculation)
     * @param {string} warpType - Type of warp
     * @param {object} modifiers - Cost modifiers
     * @returns {number} Calculated cost
     */
    calculateCost(warpType = 'basic', modifiers = {}) {
        try {
            // Get current level
            const level = this.levelManager && this.levelManager.currentLevel ?
                this.levelManager.currentLevel : 1;
            
            // Get session warps count
            const sessionWarps = this.warpState.warpCount;
            
            // Get player performance stats
            const playerStats = this.getPlayerPerformanceContext();
            
            // Calculate final cost using enhanced method
            const startTime = performance.now();
            const costResult = this.getFinalCost(warpType, level, sessionWarps, playerStats);
            let cost = costResult.cost;
            
            // Apply custom modifiers (for backward compatibility)
            if (modifiers.multiplier) {
                cost *= modifiers.multiplier;
            }
            
            if (modifiers.flatBonus) {
                cost += modifiers.flatBonus;
            }
            
            // Ensure cost is at least base cost
            const baseCost = this.calculateBaseCost(warpType, level);
            cost = Math.max(cost, baseCost);
            
            // Round to nearest integer
            cost = Math.round(cost);
            
            // Track calculation performance
            const calculationTime = performance.now() - startTime;
            this.costCalculationMetrics.calculationTimes.push(calculationTime);
            if (this.costCalculationMetrics.calculationTimes.length > 10) {
                this.costCalculationMetrics.calculationTimes.shift();
            }
            
            // Log cost calculation
            const calculationDetails = {
                baseCost: baseCost,
                level: level,
                sessionWarps: sessionWarps,
                playerStats: playerStats,
                modifiers: modifiers,
                calculationTime: calculationTime,
                complexity: this.warpConfigurations[warpType]?.complexity || 1
            };
            
            this.logCostCalculation(warpType, cost, calculationDetails);
            
            if (this.logEnabled) {
                console.log(`Warp cost calculated for ${warpType}:`, {
                    ...calculationDetails,
                    finalCost: cost
                });
            }
            
            // Trigger callback if available
            if (this.onWarpCostCalculatedCallback) {
                this.onWarpCostCalculatedCallback(warpType, cost, modifiers);
            }
            
            return cost;
            
        } catch (error) {
            console.error('Error calculating warp cost:', error);
            return this.calculateBaseCost(warpType);
        }
    }
    
    /**
     * Validate token balance for warp cost
     * @param {number} cost - Cost to validate
     * @returns {object} Validation result
     */
    validateTokenBalance(cost) {
        try {
            if (!this.tokenEconomyManager) {
                return {
                    isValid: false,
                    reason: 'token_manager_unavailable',
                    message: 'Token economy manager not available'
                };
            }
            
            return this.tokenEconomyManager.validateTokenBalance(cost);
            
        } catch (error) {
            console.error('Error validating token balance:', error);
            return {
                isValid: false,
                reason: 'validation_error',
                message: 'Error validating token balance'
            };
        }
    }
    
    /**
     * Deduct tokens for warp with proper validation
     * @param {number} cost - Cost to deduct
     * @param {string} warpType - Type of warp
     * @returns {object} Transaction result
     */
    deductTokens(cost, warpType) {
        try {
            if (!this.tokenEconomyManager) {
                return {
                    success: false,
                    reason: 'token_manager_unavailable',
                    message: 'Token economy manager not available'
                };
            }
            
            return this.tokenEconomyManager.deductTokens(cost, warpType);
            
        } catch (error) {
            console.error('Error deducting tokens:', error);
            return {
                success: false,
                reason: 'deduction_error',
                message: 'Error deducting tokens'
            };
        }
    }
    
    /**
     * Refund tokens for failed warp
     * @param {number} cost - Cost to refund
     * @param {string} warpType - Type of warp
     * @returns {object} Transaction result
     */
    refundTokens(cost, warpType) {
        try {
            if (!this.tokenEconomyManager) {
                return {
                    success: false,
                    reason: 'token_manager_unavailable',
                    message: 'Token economy manager not available'
                };
            }
            
            return this.tokenEconomyManager.refundTokens(cost, warpType);
            
        } catch (error) {
            console.error('Error refunding tokens:', error);
            return {
                success: false,
                reason: 'refund_error',
                message: 'Error refunding tokens'
            };
        }
    }
    
    /**
     * Get transaction history
     * @param {number} count - Number of recent transactions to return
     * @returns {Array} Transaction history
     */
    getTransactionHistory(count = 20) {
        if (!this.tokenEconomyManager) {
            return [];
        }
        
        return this.tokenEconomyManager.getTransactionHistory(count);
    }
    
    /**
     * Execute a reality warp
     * @param {string} warpType - Type of warp to execute
     * @param {object} options - Warp options
     * @returns {object} Warp execution result
     */
    async executeWarp(warpType = 'basic', options = {}) {
        try {
            // Check if warp can be performed
            const eligibility = this.canWarp(warpType);
            if (!eligibility.canWarp) {
                return {
                    success: false,
                    reason: eligibility.reason,
                    message: eligibility.message
                };
            }
            
            const warpConfig = this.warpConfigurations[warpType];
            if (!warpConfig) {
                return {
                    success: false,
                    reason: 'invalid_warp_type',
                    message: `Invalid warp type: ${warpType}`
                };
            }
            
            // Calculate cost
            const cost = eligibility.cost || this.calculateCost(warpType);
            
            // Validate and deduct tokens
            const validation = this.validateTokenBalance(cost);
            if (!validation.isValid) {
                return {
                    success: false,
                    reason: validation.reason,
                    message: validation.message
                };
            }
            
            const transaction = this.deductTokens(cost, warpType);
            if (!transaction.success) {
                return {
                    success: false,
                    reason: transaction.reason,
                    message: transaction.message
                };
            }
            
            // Update warp state
            this.warpState.status = 'active';
            this.warpState.activeEndTime = Date.now() + warpConfig.duration;
            this.warpState.warpCount++;
            this.warpState.lastWarpTime = Date.now();
            
            // Get user idea if provided
            const userIdea = options.userIdea || null;
            
            // Set current warp data
            this.currentWarp = {
                type: warpType,
                startTime: Date.now(),
                effects: warpConfig.effects,
                cost: cost,
                generatedPrompt: null,
                userIdea: userIdea
            };
            
            // Generate prompt if user idea is provided, otherwise fail
            if (userIdea) {
                // Use user's idea directly as the prompt
                this.currentWarp.generatedPrompt = userIdea;
            } else {
                // Fail if no user idea is provided - LLM client is required
                throw new Error('User idea is required for reality warp generation');
            }
            
            // Log warp execution
            if (this.logEnabled) {
                console.log(`Executing ${warpType} warp:`, {
                    cost: cost,
                    duration: warpConfig.duration,
                    effects: warpConfig.effects,
                    sessionWarpCount: this.warpState.warpCount,
                    userIdea: userIdea,
                    generatedPrompt: this.currentWarp.generatedPrompt
                });
            }
            
            // Trigger callback if available
            if (this.onWarpStartCallback) {
                this.onWarpStartCallback(warpType, {
                    cost: cost,
                    duration: warpConfig.duration,
                    effects: warpConfig.effects,
                    userIdea: userIdea,
                    generatedPrompt: this.currentWarp.generatedPrompt
                });
            }
            
            // If we have a user idea, try to generate an environment using backend API
            if (userIdea) {
                try {
                    console.log('🔍 [REALITY WARP DEBUG] Starting environment generation from user idea:', userIdea);
                    
                    // Call backend API to generate environment
                    const response = await fetch('http://localhost:3001/api/generate-environment', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            environmentDescription: userIdea
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const environmentResult = await response.json();
                    console.log('✅ [REALITY WARP DEBUG] Environment generated successfully:', environmentResult);
                    
                    // Apply the generated environment to the game
                    const applyResult = this.applyGeneratedEnvironment(environmentResult);
                    if (!applyResult.success) {
                        throw new Error(`Failed to apply environment: ${applyResult.message}`);
                    }
                } catch (envError) {
                    console.error('❌ [REALITY WARP DEBUG] Failed to generate environment from user idea:', envError);
                    // Fail the entire warp if environment generation fails
                    throw new Error(`Environment generation failed: ${envError.message}`);
                }
            }
            
            // Schedule warp end
            setTimeout(() => {
                this.endWarp();
            }, warpConfig.duration);
            
            // Record warp in history
            this.recordWarp(warpType, cost, Date.now(), 'success');
            
            // Update performance metrics
            this.performanceMetrics.totalWarps++;
            this.performanceMetrics.successfulWarps++;
            this.performanceMetrics.totalCost += cost;
            
            return {
                success: true,
                warpType: warpType,
                cost: cost,
                duration: warpConfig.duration,
                effects: warpConfig.effects,
                userIdea: userIdea,
                warpData: {
                    type: warpType,
                    cost: cost,
                    startTime: this.currentWarp.startTime,
                    effects: warpConfig.effects,
                    generatedPrompt: this.currentWarp.generatedPrompt,
                    userIdea: userIdea
                }
            };
            
        } catch (error) {
            console.error('Error executing warp:', error);
            
            // Record failed warp
            this.recordWarp(warpType, 0, Date.now(), 'failed');
            this.performanceMetrics.totalWarps++;
            this.performanceMetrics.failedWarps++;
            
            return {
                success: false,
                reason: 'execution_error',
                message: 'Error executing warp',
                error: error.message
            };
        }
    }
    
    /**
     * Apply generated environment data to the game
     * @param {object} environmentData - Environment data from backend API
     * @returns {object} Application result
     */
    applyGeneratedEnvironment(environmentData) {
        try {
            console.log('🔍 [REALITY WARP DEBUG] Applying generated environment:', environmentData);
            
            // Store the environment data for later use
            this.currentEnvironment = environmentData;
            
            // Update the enemy manager with the new environment modifiers
            if (window.gameEngine && window.gameEngine.enemyManager && environmentData.gameplayModifiers) {
                const environment = {
                    getEnvironmentType: () => environmentData.type || 'custom',
                    getCurrentGameplayModifiers: () => environmentData.gameplayModifiers,
                    getCurrentEnvironmentType: () => environmentData.type || 'custom',
                    getCurrentEnvironmentName: () => environmentData.name || 'Custom Environment',
                    getCurrentEnvironmentDescription: () => environmentData.description || this.currentWarp.userIdea || 'Custom Environment',
                    isEnemyCompatible: () => true,
                    getCurrentEnvironmentHazards: () => environmentData.gameplayModifiers.environmentHazards || [],
                    applyEnvironmentEffects: (stats) => stats,
                    update: () => {},
                    render: () => {},
                    resetToDefault: () => {}
                };
                
                // Add image data if available
                if (environmentData.imageData && environmentData.imageData.images && environmentData.imageData.images[0]) {
                    const imageData = environmentData.imageData.images[0];
                    // Use the local URL if available, otherwise use the external URL
                    let imageUrl = imageData.localUrl || imageData.url;
                    
                    // If the URL is relative (starts with /), prepend the server base URL
                    if (imageUrl && imageUrl.startsWith('/')) {
                        // Check if we're in development or production
                        const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
                        const serverPort = isDevelopment ? ':3001' : '';
                        const serverBaseUrl = `${window.location.protocol}//${window.location.hostname}${serverPort}`;
                        imageUrl = `${serverBaseUrl}${imageUrl}`;
                        console.log('🔍 [REALITY WARP DEBUG] Converted relative URL to full URL:', imageUrl);
                    }
                    
                    console.log('🔍 [REALITY WARP DEBUG] Setting environment image URL:', imageUrl);
                    
                    // Add a method to get the background image
                    environment.getBackgroundImage = () => imageUrl;
                }
                
                window.gameEngine.enemyManager.setEnvironment(environment);
                
                return {
                    success: true,
                    message: 'Environment applied successfully'
                };
            } else {
                console.warn('⚠️ [REALITY WARP DEBUG] Game engine or enemy manager not available');
                return {
                    success: false,
                    reason: 'game_engine_unavailable',
                    message: 'Game engine or enemy manager not available'
                };
            }
        } catch (error) {
            console.error('❌ [REALITY WARP DEBUG] Error applying generated environment:', error);
            return {
                success: false,
                reason: 'application_error',
                message: 'Error applying generated environment',
                error: error.message
            };
        }
    }
    
    /**
     * End current warp and start cooldown
     * @returns {object} Warp end result
     */
    endWarp() {
        try {
            if (this.warpState.status !== 'active') {
                return {
                    success: false,
                    reason: 'not_active',
                    message: 'No active warp to end'
                };
            }
            
            const warpType = this.currentWarp.type;
            const warpConfig = this.warpConfigurations[warpType];
            
            if (!warpConfig) {
                return {
                    success: false,
                    reason: 'invalid_warp_type',
                    message: 'Invalid warp type for current warp'
                };
            }
            
            // Calculate effectiveness
            const effectiveness = this.calculateWarpEffectiveness();
            this.updatePerformanceMetrics(effectiveness);
            
            // Update warp state to cooldown
            this.warpState.status = 'cooldown';
            this.warpState.cooldownEndTime = Date.now() + warpConfig.cooldown;
            
            // Log warp end
            if (this.logEnabled) {
                console.log(`Ending ${warpType} warp - starting cooldown:`, {
                    duration: warpConfig.cooldown,
                    effectiveness: effectiveness
                });
            }
            
            // Trigger callback if available
            if (this.onWarpEndCallback) {
                this.onWarpEndCallback(warpType, {
                    effectiveness: effectiveness,
                    duration: warpConfig.cooldown
                });
            }
            
            // Trigger cooldown start callback
            if (this.onWarpCooldownStartCallback) {
                this.onWarpCooldownStartCallback(warpType, warpConfig.cooldown);
            }
            
            // Schedule cooldown end
            setTimeout(() => {
                this.endCooldown();
            }, warpConfig.cooldown);
            
            // Reset current warp
            this.currentWarp = {
                type: null,
                startTime: 0,
                effects: [],
                cost: 0,
                generatedPrompt: null
            };
            
            return {
                success: true,
                warpType: warpType,
                effectiveness: effectiveness,
                cooldownDuration: warpConfig.cooldown
            };
            
        } catch (error) {
            console.error('Error ending warp:', error);
            return {
                success: false,
                reason: 'end_error',
                message: 'Error ending warp',
                error: error.message
            };
        }
    }
    
    /**
     * End cooldown period
     * @returns {object} Cooldown end result
     */
    endCooldown() {
        try {
            if (this.warpState.status !== 'cooldown') {
                return {
                    success: false,
                    reason: 'not_on_cooldown',
                    message: 'Not currently on cooldown'
                };
            }
            
            // Update warp state to ready
            this.warpState.status = 'ready';
            
            // Log cooldown end
            if (this.logEnabled) {
                console.log('Cooldown ended - ready for next warp');
            }
            
            // Trigger callback if available
            if (this.onWarpCooldownEndCallback) {
                this.onWarpCooldownEndCallback();
            }
            
            return {
                success: true,
                message: 'Cooldown ended'
            };
            
        } catch (error) {
            console.error('Error ending cooldown:', error);
            return {
                success: false,
                reason: 'cooldown_end_error',
                message: 'Error ending cooldown',
                error: error.message
            };
        }
    }
    
    /**
     * Generate a prompt for reality warp using LLM
     * @param {string} warpType - Type of warp
     * @param {object} options - Prompt generation options
     * @returns {string|null} Generated prompt or null if failed
     */
    generateWarpPrompt(warpType = 'basic', options = {}) {
        try {
            // This method should not be used as LLM is now handled by backend API
            throw new Error('generateWarpPrompt is deprecated. Use backend API for prompt generation.');
        } catch (error) {
            console.error('Error in deprecated generateWarpPrompt method:', error);
            throw error;
        }
    }
    
    
    /**
     * Get current difficulty level
     * @private
     * @returns {string} Current difficulty
     */
    getCurrentDifficulty() {
        if (!this.levelManager) {
            return 'normal';
        }
        
        // Simple difficulty calculation based on level
        const level = this.levelManager.currentLevel || 1;
        if (level <= 3) return 'easy';
        if (level <= 7) return 'normal';
        if (level <= 12) return 'hard';
        return 'extreme';
    }
    
    
    /**
     * Log cost calculation for debugging
     * @private
     * @param {string} warpType - Type of warp
     * @param {number} cost - Calculated cost
     * @param {object} calculationDetails - Details of the calculation
     */
    logCostCalculation(warpType, cost, calculationDetails = {}) {
        if (!this.costCalculationLogging) return;
        
        const logEntry = {
            timestamp: Date.now(),
            warpType: warpType,
            cost: cost,
            details: calculationDetails,
            sessionWarps: this.warpState.warpCount,
            warpState: this.warpState.status
        };
        
        console.log('[RealityWarpManager] Cost Calculation:', logEntry);
        
        // Track performance
        this.costCalculationMetrics.totalCalculations++;
        this.costCalculationMetrics.lastCalculationTime = Date.now();
    }
    
    /**
     * Track token transaction
     * @private
     * @param {object} transaction - Transaction data
     */
    trackTransaction(transaction) {
        if (!this.transactionLogging) return;
        
        this.transactionMetrics.totalTransactions++;
        this.transactionMetrics.lastTransactionTime = Date.now();
        
        if (transaction.success) {
            this.transactionMetrics.successfulTransactions++;
        } else {
            this.transactionMetrics.failedTransactions++;
        }
        
        if (this.debugMode) {
            console.log('[RealityWarpManager] Transaction Tracked:', {
                transaction: transaction,
                metrics: this.getTransactionMetrics()
            });
        }
    }
    
    /**
     * Get performance metrics for cost calculations
     * @returns {object} Cost calculation metrics
     */
    getCostCalculationMetrics() {
        const times = this.costCalculationMetrics.calculationTimes;
        const average = times.length > 0 ?
            times.reduce((a, b) => a + b, 0) / times.length : 0;
        
        return {
            totalCalculations: this.costCalculationMetrics.totalCalculations,
            averageCalculationTime: Math.round(average),
            lastCalculationTime: this.costCalculationMetrics.lastCalculationTime,
            calculationTimes: times.slice(-10) // Last 10 calculations
        };
    }
    
    /**
     * Get transaction metrics
     * @returns {object} Transaction metrics
     */
    getTransactionMetrics() {
        const successRate = this.transactionMetrics.totalTransactions > 0 ?
            (this.transactionMetrics.successfulTransactions / this.transactionMetrics.totalTransactions) * 100 : 0;
            
        return {
            totalTransactions: this.transactionMetrics.totalTransactions,
            successfulTransactions: this.transactionMetrics.successfulTransactions,
            failedTransactions: this.transactionMetrics.failedTransactions,
            successRate: Math.round(successRate),
            lastTransactionTime: this.transactionMetrics.lastTransactionTime
        };
    }
    
    /**
     * Get all performance metrics
     * @returns {object} Complete performance metrics
     */
    getAllPerformanceMetrics() {
        return {
            costCalculations: this.getCostCalculationMetrics(),
            transactions: this.getTransactionMetrics(),
            warpPerformance: this.getPerformanceStatistics(),
            systemState: {
                debugMode: this.debugMode,
                loggingEnabled: this.logEnabled,
                performanceTracking: this.performanceMetricsEnabled
            }
        };
    }
    
    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`RealityWarpManager debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Enable or disable cost calculation logging
     * @param {boolean} enabled - Whether cost calculation logging is enabled
     */
    setCostCalculationLogging(enabled) {
        this.costCalculationLogging = enabled;
        console.log(`RealityWarpManager cost calculation logging ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Enable or disable transaction logging
     * @param {boolean} enabled - Whether transaction logging is enabled
     */
    setTransactionLogging(enabled) {
        this.transactionLogging = enabled;
        console.log(`RealityWarpManager transaction logging ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Enable or disable performance metrics tracking
     * @param {boolean} enabled - Whether performance metrics tracking is enabled
     */
    setPerformanceMetricsTracking(enabled) {
        this.performanceMetricsEnabled = enabled;
        console.log(`RealityWarpManager performance metrics tracking ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Get current warp state
     * @returns {object} Current warp state
     */
    getWarpState() {
        return {
            status: this.warpState.status,
            warpCount: this.warpState.warpCount,
            maxWarpsPerSession: this.warpState.maxWarpsPerSession,
            currentWarp: this.currentWarp.type ? { ...this.currentWarp } : null,
            timeRemaining: this.getTimeRemaining(),
            performanceMetrics: { ...this.performanceMetrics }
        };
    }
    
    /**
     * Get time remaining for current warp or cooldown
     * @returns {object} Time remaining information
     */
    getTimeRemaining() {
        const now = Date.now();
        
        if (this.warpState.status === 'active') {
            const remaining = this.warpState.activeEndTime - now;
            return {
                type: 'active',
                remaining: Math.max(0, remaining),
                percentage: Math.min(100, (remaining / this.currentWarp.duration) * 100)
            };
        } else if (this.warpState.status === 'cooldown') {
            const remaining = this.warpState.cooldownEndTime - now;
            return {
                type: 'cooldown',
                remaining: Math.max(0, remaining),
                percentage: remaining > 0 ? 100 : 0
            };
        }
        
        return {
            type: 'ready',
            remaining: 0,
            percentage: 0
        };
    }
    
    /**
     * Reset manager state
     */
    reset() {
        try {
            console.log('Resetting RealityWarpManager state');
            
            // Reset warp state
            this.warpState = {
                status: 'ready',
                cooldownEndTime: 0,
                activeEndTime: 0,
                lastWarpTime: 0,
                warpCount: 0,
                maxWarpsPerSession: 3
            };
            
            // Reset current warp
            this.currentWarp = {
                type: null,
                startTime: 0,
                effects: [],
                cost: 0,
                generatedPrompt: null
            };
            
            // Clear warp history
            this.warpHistory = [];
            
            // Reset performance metrics
            this.performanceMetrics = {
                totalWarps: 0,
                successfulWarps: 0,
                failedWarps: 0,
                totalCost: 0,
                averageEffectiveness: 0,
                effectivenessScores: []
            };
            
            console.log('RealityWarpManager state reset successfully');
            
        } catch (error) {
            console.error('Error resetting RealityWarpManager:', error);
        }
    }
    
    /**
     * Record a warp in history
     * @private
     * @param {string} warpType - Type of warp
     * @param {number} cost - Cost of warp
     * @param {number} timestamp - Warp timestamp
     * @param {string} status - Warp status (success/failed)
     */
    recordWarp(warpType, cost, timestamp, status) {
        const warpRecord = {
            id: `warp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: warpType,
            cost: cost,
            timestamp: timestamp,
            status: status,
            level: this.levelManager ? this.levelManager.currentLevel : 1
        };
        
        this.warpHistory.push(warpRecord);
        
        // Limit history size
        if (this.warpHistory.length > this.maxHistorySize) {
            this.warpHistory.shift();
        }
    }
    
    /**
     * Calculate warp effectiveness
     * @private
     * @returns {number} Effectiveness score (0-100)
     */
    calculateWarpEffectiveness() {
        // This is a simplified effectiveness calculation
        // In a real implementation, this would consider game-specific factors
        
        const baseEffectiveness = 75; // Base effectiveness
        
        // Level bonus (higher levels = more effective warps)
        const levelBonus = this.levelManager ? Math.min(25, (this.levelManager.currentLevel - 1) * 2) : 0;
        
        // Session bonus (earlier warps in session are more effective)
        const sessionBonus = this.warpState.warpCount === 1 ? 10 : 0;
        
        // Warp type bonus
        const warpTypeBonus = {
            'basic': 0,
            'advanced': 5,
            'ultimate': 10
        };
        
        const effectiveness = Math.min(100, baseEffectiveness + levelBonus + sessionBonus + (warpTypeBonus[this.currentWarp.type] || 0));
        
        return effectiveness;
    }
    
    /**
     * Update performance metrics
     * @private
     * @param {number} effectiveness - Warp effectiveness score
     */
    updatePerformanceMetrics(effectiveness) {
        this.performanceMetrics.effectivenessScores.push(effectiveness);
        
        // Keep only last 10 scores for average calculation
        if (this.performanceMetrics.effectivenessScores.length > 10) {
            this.performanceMetrics.effectivenessScores.shift();
        }
        
        // Calculate average effectiveness
        const sum = this.performanceMetrics.effectivenessScores.reduce((a, b) => a + b, 0);
        this.performanceMetrics.averageEffectiveness = sum / this.performanceMetrics.effectivenessScores.length;
    }
    
    /**
     * Get player performance context for prompt generation
     * @private
     * @returns {object} Player performance context
     */
    getPlayerPerformanceContext() {
        if (!this.levelManager) {
            return {
                score: 0,
                accuracy: 0,
                enemiesDefeated: 0
            };
        }
        
        return {
            score: this.levelManager.currentScore,
            accuracy: this.levelManager.getAccuracy ? this.levelManager.getAccuracy() : 0,
            enemiesDefeated: this.levelManager.enemiesDefeated || 0,
            levelProgress: this.levelManager.getLevelProgress ? this.levelManager.getLevelProgress() : 0
        };
    }
    
    /**
     * Get warp configuration for a specific warp type
     * @param {string} warpType - Type of warp
     * @returns {object|null} Warp configuration or null if not found
     */
    getWarpConfiguration(warpType) {
        return this.warpConfigurations[warpType] || null;
    }
    
    /**
     * Get all available warp configurations
     * @returns {object} All warp configurations
     */
    getAllWarpConfigurations() {
        return { ...this.warpConfigurations };
    }
    
    /**
     * Get warp history
     * @param {number} count - Number of recent records to return
     * @returns {Array} Warp history
     */
    getWarpHistory(count = 10) {
        return this.warpHistory
            .slice(-count)
            .reverse(); // Most recent first
    }
    
    /**
     * Get performance statistics
     * @returns {object} Performance statistics
     */
    getPerformanceStatistics() {
        const successRate = this.performanceMetrics.totalWarps > 0 
            ? (this.performanceMetrics.successfulWarps / this.performanceMetrics.totalWarps) * 100 
            : 0;
        
        const averageCost = this.performanceMetrics.successfulWarps > 0
            ? this.performanceMetrics.totalCost / this.performanceMetrics.successfulWarps
            : 0;
        
        return {
            successRate: Math.round(successRate),
            totalWarps: this.performanceMetrics.totalWarps,
            successfulWarps: this.performanceMetrics.successfulWarps,
            failedWarps: this.performanceMetrics.failedWarps,
            totalCost: this.performanceMetrics.totalCost,
            averageCost: Math.round(averageCost),
            averageEffectiveness: Math.round(this.performanceMetrics.averageEffectiveness),
            sessionWarps: this.warpState.warpCount,
            maxSessionWarps: this.warpState.maxWarpsPerSession
        };
    }
    
    /**
     * Set event callback
     * @param {string} eventType - Type of event ('warpStart', 'warpEnd', 'cooldownStart', 'cooldownEnd', 'costCalculated', 'promptGenerated')
     * @param {function} callback - Callback function
     */
    setCallback(eventType, callback) {
        switch (eventType) {
            case 'warpStart':
                this.onWarpStartCallback = callback;
                break;
            case 'warpEnd':
                this.onWarpEndCallback = callback;
                break;
            case 'cooldownStart':
                this.onWarpCooldownStartCallback = callback;
                break;
            case 'cooldownEnd':
                this.onWarpCooldownEndCallback = callback;
                break;
            case 'costCalculated':
                this.onWarpCostCalculatedCallback = callback;
                break;
            case 'promptGenerated':
                this.onPromptGeneratedCallback = callback;
                break;
            default:
                console.warn(`Unknown event type: ${eventType}`);
        }
    }
    
    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`RealityWarpManager debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Enable or disable logging
     * @param {boolean} enabled - Whether logging is enabled
     */
    setLogging(enabled) {
        this.logEnabled = enabled;
        console.log(`RealityWarpManager logging ${enabled ? 'enabled' : 'disabled'}`);
    }
}