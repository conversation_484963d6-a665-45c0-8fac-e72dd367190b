import { PowerUpFactory } from '../systems/PowerUp.js';

/**
 * GenieInterface - Modal interface for purchasing power-ups and reality warps between levels
 * Provides a mystical Genie-themed UI for player purchases using WISH tokens
 */
export class GenieInterface {
    constructor(tokenManager, gameEngine) {
        this.tokenManager = tokenManager;
        this.gameEngine = gameEngine;
        
        // UI state
        this.isVisible = false;
        this.isInitialized = false;
        this.container = null;
        
        // Available power-ups
        this.availablePowerUps = PowerUpFactory.createAllPowerUps();
        this.activePowerUps = new Map(); // Track active power-ups by type
        
        // Callbacks
        this.onPowerUpPurchased = null;
        this.onWarpPurchased = null;
        this.onClose = null;

        // Animation state
        this.animationFrame = null;
        this.glowAnimation = 0;

        // Feedback system
        this.feedbackMessages = [];
        this.feedbackTimeout = null;
        
        console.log('GenieInterface created');
    }
    
    /**
     * Initialize the Genie interface
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Create container element
            this.container = document.createElement('div');
            this.container.id = 'genie-interface';
            this.container.className = 'genie-interface hidden';
            
            // Add to document body
            document.body.appendChild(this.container);
            
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('GenieInterface initialized successfully');
            
        } catch (error) {
            console.error('GenieInterface initialization error:', error);
            throw error;
        }
    }
    
    /**
     * Show the Genie interface
     */
    show() {
        if (!this.isInitialized) {
            console.error('GenieInterface not initialized');
            return;
        }
        
        this.isVisible = true;
        this.updatePowerUpAvailability();
        this.render();
        this.container.classList.remove('hidden');
        this.container.classList.add('visible');
        
        // Start glow animation
        this.startGlowAnimation();
        
        console.log('GenieInterface shown');
    }
    
    /**
     * Hide the Genie interface
     */
    hide() {
        if (!this.isVisible) return;
        
        this.isVisible = false;
        this.container.classList.remove('visible');
        this.container.classList.add('hidden');
        
        // Stop glow animation
        this.stopGlowAnimation();
        
        console.log('GenieInterface hidden');
    }
    
    /**
     * Update power-up availability based on current game state
     */
    updatePowerUpAvailability() {
        const playerTokens = this.tokenManager.getBalance();
        const playerShip = this.gameEngine.playerShip;
        
        this.availablePowerUps.forEach(powerUp => {
            const availability = powerUp.canPurchase(playerShip, playerTokens);
            powerUp.availability = availability;
        });
    }
    
    /**
     * Render the Genie interface
     */
    render() {
        if (!this.container) return;
        
        const playerTokens = this.tokenManager.getBalance();
        
        this.container.innerHTML = `
            <div class="genie-modal">
                <div class="genie-backdrop" id="genie-backdrop"></div>
                <div class="genie-content">
                    <div class="genie-header">
                        <div class="genie-character">
                            <div class="genie-lamp">🪔</div>
                            <div class="genie-smoke"></div>
                        </div>
                        <h1 class="genie-title">The Mystical Genie</h1>
                        <p class="genie-subtitle">Your wishes are my command, traveler...</p>
                        <div class="token-display">
                            <span class="token-icon">✨</span>
                            <span class="token-amount">${playerTokens}</span>
                            <span class="token-label">WISH Tokens</span>
                        </div>
                    </div>
                    
                    <div class="genie-body">
                        <div class="power-ups-section">
                            <h2 class="section-title">Power-Up Enchantments</h2>
                            <div class="power-ups-grid">
                                ${this.renderPowerUps()}
                            </div>
                        </div>
                        
                        <div class="reality-warp-section">
                            <h2 class="section-title">Reality Warp Magic</h2>
                            <div class="warp-options">
                                ${this.renderRealityWarps()}
                            </div>
                        </div>
                    </div>
                    
                    <div class="genie-footer">
                        <button id="genie-close-btn" class="genie-button secondary">
                            Continue Journey
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Set up button event listeners
        this.setupButtonEventListeners();
    }
    
    /**
     * Render power-up cards
     */
    renderPowerUps() {
        return this.availablePowerUps.map(powerUp => {
            // Skip Reality Warp power-ups in the general section - they have their own dedicated section
            if (powerUp.type.startsWith('REALITY_WARP')) {
                return '';
            }
            
            const availability = powerUp.availability || { canPurchase: false, reason: 'unknown' };
            const isActive = this.activePowerUps.has(powerUp.type);
            const canPurchase = availability.canPurchase && !isActive;
            
            let statusClass = '';
            let statusText = '';
            let buttonText = `Purchase (${powerUp.cost} ✨)`;
            
            if (isActive) {
                statusClass = 'active';
                statusText = 'Active';
                buttonText = 'Already Active';
            } else if (!availability.canPurchase) {
                statusClass = 'unavailable';
                if (availability.reason === 'insufficient_tokens') {
                    statusText = 'Insufficient Tokens';
                    buttonText = `Need ${powerUp.cost - this.tokenManager.getBalance()} more ✨`;
                } else {
                    statusText = 'Unavailable';
                    buttonText = 'Cannot Purchase';
                }
            }
            
            return `
                <div class="power-up-card ${statusClass}" data-power-up="${powerUp.type}">
                    <div class="power-up-icon">${powerUp.icon}</div>
                    <h3 class="power-up-name">${this.formatPowerUpName(powerUp.type)}</h3>
                    <p class="power-up-description">${powerUp.description}</p>
                    <div class="power-up-details">
                        <div class="power-up-cost">
                            <span class="cost-amount">${powerUp.cost}</span>
                            <span class="cost-icon">✨</span>
                        </div>
                        <div class="power-up-duration">
                            ${powerUp.duration ? `${Math.ceil(powerUp.duration / 1000)}s` : 'Permanent'}
                        </div>
                    </div>
                    <div class="power-up-status">${statusText}</div>
                    <button class="power-up-button ${canPurchase ? 'primary' : 'disabled'}"
                            data-power-up="${powerUp.type}"
                            ${!canPurchase ? 'disabled' : ''}>
                        ${buttonText}
                    </button>
                </div>
            `;
        }).join('');
    }
    
    /**
     * Format power-up type name for display
     */
    formatPowerUpName(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return 'Extra Life';
            case 'SPREAD_AMMO':
                return 'Spread Ammo';
            case 'EXTRA_WINGMAN':
                return 'Extra Wingman';
            case 'REALITY_WARP':
                return 'Reality Warp';
            default:
                return type.replace(/_/g, ' ').toLowerCase()
                    .replace(/\b\w/g, l => l.toUpperCase());
        }
    }
    
    /**
     * Render Reality Warp cards
     */
    renderRealityWarps() {
        const warpPowerUps = this.availablePowerUps.filter(p =>
            p.type.startsWith('REALITY_WARP')
        );
        
        return warpPowerUps.map(warp => {
            const availability = warp.availability || { canPurchase: false, reason: 'unknown' };
            const isActive = this.activePowerUps.has(warp.type);
            const canPurchase = availability.canPurchase && !isActive;
            
            let statusClass = '';
            let statusText = '';
            let buttonText = `Purchase (${warp.cost} ✨)`;
            
            if (isActive) {
                statusClass = 'active';
                statusText = 'Active';
                buttonText = 'Already Active';
            } else if (!availability.canPurchase) {
                statusClass = 'unavailable';
                if (availability.reason === 'insufficient_tokens') {
                    statusText = 'Insufficient Tokens';
                    buttonText = `Need ${warp.cost - this.tokenManager.getBalance()} more ✨`;
                } else {
                    statusText = 'Unavailable';
                    buttonText = 'Cannot Purchase';
                }
            }
            
            return `
                <div class="warp-card ${statusClass}" data-power-up="${warp.type}">
                    <div class="warp-icon">${warp.icon}</div>
                    <h3 class="warp-name">Reality Warp</h3>
                    <p class="warp-description">${warp.description}</p>
                    <div class="warp-details">
                        <div class="warp-cost">
                            <span class="cost-amount">${warp.cost}</span>
                            <span class="cost-icon">✨</span>
                        </div>
                        <div class="warp-tier">Single Level</div>
                    </div>
                    <div class="warp-status">${statusText}</div>
                    <button class="warp-button ${canPurchase ? 'primary' : 'disabled'}"
                            data-power-up="${warp.type}"
                            ${!canPurchase ? 'disabled' : ''}>
                        ${buttonText}
                    </button>
                </div>
            `;
        }).join('');
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Handle escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.close();
            }
        });
    }
    
    /**
     * Set up button event listeners after rendering
     */
    setupButtonEventListeners() {
        // Close button
        const closeBtn = this.container.querySelector('#genie-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }
        
        // Backdrop click to close
        const backdrop = this.container.querySelector('#genie-backdrop');
        if (backdrop) {
            backdrop.addEventListener('click', () => this.close());
        }
        
        // Power-up purchase buttons
        const powerUpButtons = this.container.querySelectorAll('.power-up-button:not(.disabled)');
        powerUpButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const powerUpType = e.target.dataset.powerUp;
                this.handlePowerUpPurchase(powerUpType);
            });
        });
        
        // Warp purchase buttons
        const warpButtons = this.container.querySelectorAll('.warp-button:not(.disabled)');
        warpButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const powerUpType = e.target.dataset.powerUp;
                this.handlePowerUpPurchase(powerUpType);
            });
        });
    }
    
    /**
     * Handle power-up purchase
     */
    async handlePowerUpPurchase(powerUpType) {
        const powerUp = this.availablePowerUps.find(p => p.type === powerUpType);
        if (!powerUp) {
            console.error('Power-up not found:', powerUpType);
            return;
        }

        // Check if purchase is valid
        const playerTokens = this.tokenManager.getBalance();
        const playerShip = this.gameEngine.playerShip;
        const availability = powerUp.canPurchase(playerShip, playerTokens);

        if (!availability.canPurchase) {
            console.warn('Cannot purchase power-up:', availability.reason);

            // Show appropriate error message
            let errorMessage = 'Cannot purchase power-up';
            switch (availability.reason) {
                case 'insufficient_tokens':
                    errorMessage = `Not enough WISH tokens! Need ${powerUp.cost}, have ${playerTokens}`;
                    break;
                case 'already_active':
                    errorMessage = 'Power-up is already active!';
                    break;
                default:
                    errorMessage = `Cannot purchase: ${availability.reason}`;
            }

            this.showFeedback(errorMessage, 'error');
            return;
        }

        // Spend tokens
        const spendResult = this.tokenManager.spendTokens(powerUp.cost, `power_up_${powerUpType.toLowerCase()}`);
        if (!spendResult.success) {
            console.error('Failed to spend tokens:', spendResult.reason);
            this.showFeedback('Transaction failed! Please try again.', 'error');
            return;
        }

        // Apply power-up (now async)
        const applyResult = await powerUp.apply(playerShip);
        if (!applyResult) {
            console.error('Failed to apply power-up');
            // Refund tokens
            this.tokenManager.awardTokens(powerUp.cost, 'power_up_refund');
            this.showFeedback('Power-up activation failed! Tokens refunded.', 'error');
            return;
        }

        // Track active power-up
        this.activePowerUps.set(powerUpType, powerUp);

        // Check if this is a Reality Warp and collect user input
        if (powerUpType === 'REALITY_WARP') {
            // Show input dialog for Reality Warp idea
            // Note: The Reality Warp execution now happens inside collectRealityWarpIdea
            const userIdea = await this.collectRealityWarpIdea();
            if (!userIdea) {
                // User cancelled or provided empty input
                // Refund tokens
                this.tokenManager.awardTokens(powerUp.cost, 'reality_warp_cancelled');
                this.showFeedback('Reality Warp cancelled. Tokens refunded.', 'info');
                return;
            }
            
            // If we get here, the Reality Warp was successful
            console.log('Reality Warp executed with user idea:', userIdea);
            this.showFeedback('Reality Warp activated with your idea!', 'success');
        }

        // Update UI
        this.updatePowerUpAvailability();
        this.render();

        // Trigger callback
        if (this.onPowerUpPurchased) {
            this.onPowerUpPurchased(powerUp, spendResult);
        }

        console.log(`Power-up purchased: ${powerUpType} for ${powerUp.cost} tokens`);

        // Show success feedback
        const powerUpName = this.formatPowerUpName(powerUpType);
        this.showFeedback(`${powerUpName} activated! (-${powerUp.cost} tokens)`, 'success');
    }
    
    /**
     * Collect user's idea for Reality Warp
     * @returns {Promise<string|null>} User's idea or null if cancelled
     */
    async collectRealityWarpIdea() {
        return new Promise((resolve) => {
            let overlay = null;
            let textarea = null;
            let confirmButton = null;
            let cancelButton = null;
            let isGenerating = false;
            
            const cleanup = () => {
                if (overlay && overlay.parentNode) {
                    document.body.removeChild(overlay);
                }
                const style = document.querySelector('style[data-reality-warp-style]');
                if (style) {
                    document.head.removeChild(style);
                }
            };
            
            const showLoadingState = () => {
                if (!overlay) return;
                
                const modal = overlay.querySelector('.reality-warp-input-modal');
                modal.innerHTML = `
                    <div class="reality-warp-input-header">
                        <h3>Generating Environment...</h3>
                        <p>Please wait while we create your custom environment...</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Creating your reality...</div>
                    </div>
                `;
                
                // Add loading styles
                const loadingStyle = document.createElement('style');
                loadingStyle.textContent = `
                    .loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 4px solid #475569;
                        border-top: 4px solid #6366f1;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 20px auto;
                    }
                    .loading-text {
                        text-align: center;
                        color: #cbd5e1;
                        font-size: 14px;
                        margin-top: 10px;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(loadingStyle);
            };
            
            // Create modal overlay
            overlay = document.createElement('div');
            overlay.className = 'reality-warp-input-overlay';
            overlay.innerHTML = `
                <div class="reality-warp-input-modal">
                    <div class="reality-warp-input-header">
                        <h3>Reality Warp</h3>
                        <p>Describe the environment you want to create for the next level:</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <textarea
                            id="reality-warp-idea"
                            class="reality-warp-textarea"
                            placeholder="e.g., A mystical forest with floating islands and ancient ruins..."
                            rows="4"
                            maxlength="200"
                        ></textarea>
                        <div class="reality-warp-input-footer">
                            <button id="reality-warp-cancel" class="genie-button secondary">Cancel</button>
                            <button id="reality-warp-confirm" class="genie-button primary">Create Reality</button>
                        </div>
                    </div>
                </div>
            `;
            
            // Add styles for the modal
            const style = document.createElement('style');
            style.setAttribute('data-reality-warp-style', 'true');
            style.textContent = `
                .reality-warp-input-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }
                .reality-warp-input-modal {
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    border: 2px solid #6366f1;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
                }
                .reality-warp-input-header h3 {
                    color: #818cf8;
                    margin: 0 0 8px 0;
                    font-size: 24px;
                    text-align: center;
                }
                .reality-warp-input-header p {
                    color: #cbd5e1;
                    margin: 0 0 16px 0;
                    text-align: center;
                    font-size: 14px;
                }
                .reality-warp-textarea {
                    width: 100%;
                    background: rgba(30, 41, 59, 0.8);
                    border: 1px solid #475569;
                    border-radius: 8px;
                    color: #e2e8f0;
                    padding: 12px;
                    font-size: 14px;
                    resize: vertical;
                    margin-bottom: 16px;
                }
                .reality-warp-textarea:focus {
                    outline: none;
                    border-color: #6366f1;
                    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
                }
                .reality-warp-input-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                }
                .genie-button {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .genie-button.primary {
                    background: #6366f1;
                    color: white;
                }
                .genie-button.primary:hover {
                    background: #4f46e5;
                }
                .genie-button.secondary {
                    background: #475569;
                    color: #e2e8f0;
                }
                .genie-button.secondary:hover {
                    background: #334155;
                }
                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #475569;
                    border-top: 4px solid #6366f1;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 20px auto;
                }
                .loading-text {
                    text-align: center;
                    color: #cbd5e1;
                    font-size: 14px;
                    margin-top: 10px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            
            // Add to document
            document.head.appendChild(style);
            document.body.appendChild(overlay);
            
            // Get elements
            textarea = document.getElementById('reality-warp-idea');
            confirmButton = document.getElementById('reality-warp-confirm');
            cancelButton = document.getElementById('reality-warp-cancel');
            
            // Focus on textarea
            textarea.focus();
            
            
            cancelButton.addEventListener('click', () => {
                cleanup();
                resolve(null);
            });
            
            confirmButton.addEventListener('click', async () => {
                const idea = textarea.value.trim();
                if (!idea) {
                    resolve(null);
                    cleanup();
                    return;
                }
                
                // Show loading state
                isGenerating = true;
                confirmButton.disabled = true;
                cancelButton.disabled = true;
                
                const modal = overlay.querySelector('.reality-warp-input-modal');
                modal.innerHTML = `
                    <div class="reality-warp-input-header">
                        <h3>Generating Environment...</h3>
                        <p>Please wait while we create your custom environment...</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Creating your reality...</div>
                    </div>
                `;
                
                try {
                    // Generate environment by sending user input to backend API
                    const response = await fetch('http://localhost:3001/api/generate-environment', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            environmentDescription: idea
                        })
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || 'Failed to generate environment');
                    }
                    
                    const result = await response.json();
                    
                    if (result) {
                        console.log('Reality Warp executed with user idea:', idea);
                        
                        // Update the game with the generated environment
                        if (this.gameEngine && this.gameEngine.realityWarpManager) {
                            // Apply the generated environment to the game
                            this.gameEngine.realityWarpManager.applyGeneratedEnvironment(result);
                        }
                        
                        resolve(idea);
                    } else {
                        console.warn(`Reality Warp execution failed: Invalid response`);
                        this.showFeedback && this.showFeedback('Reality Warp failed: Invalid response from server', 'error');
                        resolve(null);
                    }
                } catch (error) {
                    console.error('Error during reality warp:', error);
                    this.showFeedback && this.showFeedback('Reality Warp failed due to an error', 'error');
                    resolve(null);
                } finally {
                    cleanup();
                }
            });
            
            // Handle keyboard events
            textarea.addEventListener('keydown', (e) => {
                // Allow WASDE keys and other normal typing
                if (e.key.length === 1 && !e.ctrlKey && !e.altKey && !e.metaKey) {
                    // Let normal character input proceed
                    return;
                }
                
                // Handle Enter key (but allow Shift+Enter for new line)
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    confirmButton.click();
                }
                
                // Handle Escape key
                if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelButton.click();
                }
                
                // Allow arrow keys, backspace, delete, home, end, etc.
                if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Backspace', 'Delete', 'Home', 'End', 'Tab'].includes(e.key)) {
                    return;
                }
            });
        });
    }
    
    /**
     * Close the interface
     */
    close() {
        this.hide();

        // Ensure all feedback messages are cleared when closing
        this.feedbackMessages = [];

        if (this.onClose) {
            this.onClose();
        }
    }

    /**
     * Show feedback message to user
     * @param {string} message - Message to display
     * @param {string} type - Message type ('success', 'error', 'warning')
     * @param {number} duration - Duration in milliseconds
     */
    showFeedback(message, type = 'info', duration = 3000) {
        const feedback = {
            id: Date.now(),
            message,
            type,
            timestamp: Date.now(),
            duration
        };

        this.feedbackMessages.push(feedback);
        this.renderFeedback();

        // Auto-remove after duration
        setTimeout(() => {
            this.removeFeedback(feedback.id);
        }, duration);
    }

    /**
     * Remove feedback message by ID
     * @param {number} id - Feedback message ID
     */
    removeFeedback(id) {
        this.feedbackMessages = this.feedbackMessages.filter(f => f.id !== id);
        this.renderFeedback();
    }

    /**
     * Render feedback messages
     */
    renderFeedback() {
        if (!this.container) return;

        // Remove existing feedback container
        const existingFeedback = this.container.querySelector('.genie-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // Create new feedback container if there are messages
        if (this.feedbackMessages.length > 0) {
            const feedbackContainer = document.createElement('div');
            feedbackContainer.className = 'genie-feedback';
            feedbackContainer.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-width: 300px;
            `;

            // Add each feedback message
            this.feedbackMessages.forEach(feedback => {
                const messageEl = document.createElement('div');
                messageEl.className = `feedback-message feedback-${feedback.type}`;

                const typeColors = {
                    success: { bg: 'rgba(0, 255, 136, 0.9)', border: '#00ff88' },
                    error: { bg: 'rgba(255, 68, 68, 0.9)', border: '#ff4444' },
                    warning: { bg: 'rgba(255, 170, 0, 0.9)', border: '#ffaa00' },
                    info: { bg: 'rgba(0, 255, 255, 0.9)', border: '#00ffff' }
                };

                const colors = typeColors[feedback.type] || typeColors.info;

                messageEl.style.cssText = `
                    background: ${colors.bg};
                    border: 2px solid ${colors.border};
                    border-radius: 8px;
                    padding: 12px 16px;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    animation: feedbackSlideIn 0.3s ease-out;
                    backdrop-filter: blur(5px);
                `;

                messageEl.textContent = feedback.message;
                feedbackContainer.appendChild(messageEl);
            });

            this.container.appendChild(feedbackContainer);
        }
    }
    
    /**
     * Start glow animation
     */
    startGlowAnimation() {
        if (this.animationFrame) return;
        
        const animate = () => {
            this.glowAnimation += 0.05;
            
            // Apply glow effect to lamp and tokens
            const lamp = this.container.querySelector('.genie-lamp');
            const tokenIcon = this.container.querySelector('.token-icon');
            
            if (lamp) {
                const glow = Math.sin(this.glowAnimation) * 0.5 + 0.5;
                lamp.style.filter = `drop-shadow(0 0 ${10 + glow * 10}px #ffd700)`;
            }
            
            if (tokenIcon) {
                const glow = Math.sin(this.glowAnimation + 1) * 0.5 + 0.5;
                tokenIcon.style.filter = `drop-shadow(0 0 ${5 + glow * 5}px #00ffff)`;
            }
            
            if (this.isVisible) {
                this.animationFrame = requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * Stop glow animation
     */
    stopGlowAnimation() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }
    
    /**
     * Update active power-ups (call from game loop)
     */
    updateActivePowerUps(deltaTime) {
        const playerShip = this.gameEngine.playerShip;
        
        for (const [type, powerUp] of this.activePowerUps) {
            const stillActive = powerUp.update(deltaTime, playerShip);
            
            if (!stillActive) {
                this.activePowerUps.delete(type);
                console.log(`Power-up expired: ${type}`);
            }
        }
    }
    
    /**
     * Set callbacks
     */
    setOnPowerUpPurchased(callback) {
        this.onPowerUpPurchased = callback;
    }
    
    setOnWarpPurchased(callback) {
        this.onWarpPurchased = callback;
    }
    
    setOnClose(callback) {
        this.onClose = callback;
    }

    /**
     * Format power-up type name for display
     * @param {string} type - Power-up type
     * @returns {string} Formatted name
     */
    formatPowerUpName(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return 'Extra Life';
            case 'SPREAD_AMMO':
                return 'Spread Ammo';
            case 'EXTRA_WINGMAN':
                return 'Wingman';
            default:
                return type.replace(/_/g, ' ').toLowerCase()
                    .replace(/\b\w/g, l => l.toUpperCase());
        }
    }
    
    /**
     * Cleanup resources
     */
    destroy() {
        this.stopGlowAnimation();
        
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.container = null;
        this.isInitialized = false;
        
        console.log('GenieInterface destroyed');
    }
}
